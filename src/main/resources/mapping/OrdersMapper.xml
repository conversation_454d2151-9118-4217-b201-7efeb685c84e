<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springbootalipay.mapper.OrdersMapper" >
  <resultMap id="BaseResultMap" type="qidian.it.springbootalipay.entity.Orders" >
    <id column="order_id" property="order_id" jdbcType="INTEGER" />
    <result column="user_id" property="user_id" jdbcType="INTEGER" />
    <result column="total_price" property="total_price" jdbcType="DOUBLE" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="create_time" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    order_id, user_id, total_price, status, create_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from orders
    where order_id = #{order_id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from orders
    where order_id = #{order_id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="qidian.it.springbootalipay.entity.Orders" >
    insert into orders (order_id, user_id, total_price, 
      status, create_time)
    values (#{order_id,jdbcType=INTEGER}, #{user_id,jdbcType=INTEGER}, #{total_price,jdbcType=DOUBLE}, 
      #{status,jdbcType=INTEGER}, #{create_time,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="qidian.it.springbootalipay.entity.Orders" >
    insert into orders
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="order_id != null" >
        order_id,
      </if>
      <if test="user_id != null" >
        user_id,
      </if>
      <if test="total_price != null" >
        total_price,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="create_time != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="order_id != null" >
        #{order_id,jdbcType=INTEGER},
      </if>
      <if test="user_id != null" >
        #{user_id,jdbcType=INTEGER},
      </if>
      <if test="total_price != null" >
        #{total_price,jdbcType=DOUBLE},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="create_time != null" >
        #{create_time,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="qidian.it.springbootalipay.entity.Orders" >
    update orders
    <set >
      <if test="user_id != null" >
        user_id = #{user_id,jdbcType=INTEGER},
      </if>
      <if test="total_price != null" >
        total_price = #{total_price,jdbcType=DOUBLE},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="create_time != null" >
        create_time = #{create_time,jdbcType=TIMESTAMP},
      </if>
    </set>
    where order_id = #{order_id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="qidian.it.springbootalipay.entity.Orders" >
    update orders
    set user_id = #{user_id,jdbcType=INTEGER},
      total_price = #{total_price,jdbcType=DOUBLE},
      status = #{status,jdbcType=INTEGER},
      create_time = #{create_time,jdbcType=TIMESTAMP}
    where order_id = #{order_id,jdbcType=INTEGER}
  </update>
</mapper>