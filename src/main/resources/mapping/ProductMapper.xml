<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springbootalipay.mapper.ProductMapper" >
  <resultMap id="BaseResultMap" type="qidian.it.springbootalipay.entity.Product" >
    <id column="product_id" property="product_id" jdbcType="INTEGER" />
    <result column="product_name" property="product_name" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="storage_num" property="storage_num" jdbcType="INTEGER" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="product_images" property="product_images" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    product_id, product_name, price, storage_num, description, product_images
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from product
    where product_id = #{product_id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from product
    where product_id = #{product_id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="qidian.it.springbootalipay.entity.Product" >
    insert into product (product_id, product_name, price, 
      storage_num, description, product_images
      )
    values (#{product_id,jdbcType=INTEGER}, #{product_name,jdbcType=VARCHAR}, #{price,jdbcType=DOUBLE}, 
      #{storage_num,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, #{product_images,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="qidian.it.springbootalipay.entity.Product" >
    insert into product
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="product_id != null" >
        product_id,
      </if>
      <if test="product_name != null" >
        product_name,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="storage_num != null" >
        storage_num,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="product_images != null" >
        product_images,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="product_id != null" >
        #{product_id,jdbcType=INTEGER},
      </if>
      <if test="product_name != null" >
        #{product_name,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="storage_num != null" >
        #{storage_num,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="product_images != null" >
        #{product_images,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="qidian.it.springbootalipay.entity.Product" >
    update product
    <set >
      <if test="product_name != null" >
        product_name = #{product_name,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="storage_num != null" >
        storage_num = #{storage_num,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="product_images != null" >
        product_images = #{product_images,jdbcType=VARCHAR},
      </if>
    </set>
    where product_id = #{product_id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="qidian.it.springbootalipay.entity.Product" >
    update product
    set product_name = #{product_name,jdbcType=VARCHAR},
      price = #{price,jdbcType=DOUBLE},
      storage_num = #{storage_num,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      product_images = #{product_images,jdbcType=VARCHAR}
    where product_id = #{product_id,jdbcType=INTEGER}
  </update>
</mapper>