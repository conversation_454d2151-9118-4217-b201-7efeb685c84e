<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springbootalipay.mapper.UserMapper" >
  <resultMap id="BaseResultMap" type="qidian.it.springbootalipay.entity.User" >
    <id column="user_id" property="user_id" jdbcType="INTEGER" />
    <result column="username" property="username" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    user_id, username, password, email
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from user
    where user_id = #{user_id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from user
    where user_id = #{user_id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="qidian.it.springbootalipay.entity.User" >
    insert into user (user_id, username, password, 
      email)
    values (#{user_id,jdbcType=INTEGER}, #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="qidian.it.springbootalipay.entity.User" >
    insert into user
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="user_id != null" >
        user_id,
      </if>
      <if test="username != null" >
        username,
      </if>
      <if test="password != null" >
        password,
      </if>
      <if test="email != null" >
        email,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="user_id != null" >
        #{user_id,jdbcType=INTEGER},
      </if>
      <if test="username != null" >
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        #{email,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="qidian.it.springbootalipay.entity.User" >
    update user
    <set >
      <if test="username != null" >
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        email = #{email,jdbcType=VARCHAR},
      </if>
    </set>
    where user_id = #{user_id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="qidian.it.springbootalipay.entity.User" >
    update user
    set username = #{username,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR}
    where user_id = #{user_id,jdbcType=INTEGER}
  </update>
</mapper>