server:
  port: 8082

spring:
  application:
    name: springboot
  datasource:
    username: root
    password: 123456
    url: ***********************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-timeout: 6000
      maximum-pool-size: 5

mybatis:
  # ?? mapper.xml ???
  mapper-locations: classpath:mapping/*.xml
  #????????,?????????????? mapper.xml ??????????????
  type-aliases-package: entity
  configuration:
    #???????????????????
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl