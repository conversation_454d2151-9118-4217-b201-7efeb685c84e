package qidian.it.springbootalipay.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradePagePayRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springbootalipay.config.AliPayConfig;
import qidian.it.springbootalipay.entity.Product;
import qidian.it.springbootalipay.entity.Result;
import qidian.it.springbootalipay.service.impl.ProductServiceImpl;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@RestController
@CrossOrigin
public class ProductController {

    @Autowired
    ProductServiceImpl productService;

    @RequestMapping(value = "/getProductList", produces = "application/json;charset=utf-8")
    public Result getProductList() {
        return productService.getProductList();
    }

    @RequestMapping("/pay")
    public void pay(Integer productId,HttpServletResponse resp) {
        System.out.println("进入pay");
        Product product= (Product) productService.getProductById(productId).getData();

        System.out.println(product);
//获得初始化的AlipayClient
        AlipayClient alipayClient = new DefaultAlipayClient(AliPayConfig.GATEWAY_URL,
                AliPayConfig.APP_ID, AliPayConfig.MERCHANT_PRIVATE_KEY,
                "json", AliPayConfig.CHARSET, AliPayConfig.ALIPAY_PUBLIC_KEY, AliPayConfig.SIGN_TYPE);
        //设置请求参数
        AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
        alipayRequest.setReturnUrl(AliPayConfig.RETURN_URL);  //设置同步回调通知
        alipayRequest.setNotifyUrl(AliPayConfig.NOTIFY_URL);  //设置异步回调通知
        //订单编号
        String out_trade_no = String.valueOf(System.currentTimeMillis());

        //价格
        String total_amount =product.getPrice().toString();

        //商品的标题/交易标题/订单标题/订单关键字等
        String subject = product.getProductName();

        String body = product.getDescription();

        //设置支付参数
        alipayRequest.setBizContent("{\"out_trade_no\":\"" + out_trade_no + "\","
                + "\"total_amount\":\"" + total_amount + "\","
                + "\"subject\":\"" + subject + "\","
                + "\"body\":\"" + body + "\","
                + "\"product_code\":\"FAST_INSTANT_TRADE_PAY\"}");
        //请求
        String result = null;
        try {
            result = alipayClient.pageExecute(alipayRequest).getBody();

            System.out.println("结果===>" + result);
            resp.setContentType("text/html;charset=UTF-8");
            PrintWriter printWriter = resp.getWriter();
            printWriter.println(result);
            printWriter.flush();
            //无效代码
            //生产订单.....
        } catch (AlipayApiException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }



}
