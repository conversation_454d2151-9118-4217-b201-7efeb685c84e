package qidian.it.springbootalipay.controller;

import org.springframework.web.bind.annotation.*;
import qidian.it.springbootalipay.entity.Admin;
import qidian.it.springbootalipay.entity.Result;

import java.util.HashMap;
import java.util.Map;

/**
 * Admin前端控制器 - 简化版本
 */
@RestController
@CrossOrigin
@RequestMapping("/admin")
public class AdminController {

    /**
     * 简单的登录接口 - 为了兼容前端路由守卫
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    @RequestMapping("/login")
    public Result login(@RequestParam String username, @RequestParam String password) {
        // 简单验证 - 实际项目中应该查询数据库
        if (username != null && !username.trim().isEmpty() &&
            password != null && !password.trim().isEmpty()) {

            // 创建管理员对象
            Admin admin = new Admin();
            admin.setId(1L);
            admin.setUsername(username);
            admin.setEmail("<EMAIL>");

            // 创建包含用户信息和token的返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("admin", admin);
            data.put("token", "simple-token-" + System.currentTimeMillis());

            return Result.success("登录成功", data);
        } else {
            return Result.fail("用户名或密码不能为空");
        }
    }

    /**
     * 简单的注册接口
     * @param username 用户名
     * @param password 密码
     * @param email 邮箱
     * @return 注册结果
     */
    @RequestMapping("/register")
    public Result register(@RequestParam String username,
                          @RequestParam String password,
                          @RequestParam String email) {
        // 简单验证
        if (username != null && !username.trim().isEmpty() &&
            password != null && !password.trim().isEmpty() &&
            email != null && !email.trim().isEmpty()) {

            return Result.success("注册成功");
        } else {
            return Result.fail("所有字段都不能为空");
        }
    }
}
