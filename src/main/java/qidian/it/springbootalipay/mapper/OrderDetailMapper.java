package qidian.it.springbootalipay.mapper;

import qidian.it.springbootalipay.entity.OrderDetail;

public interface OrderDetailMapper {
    int deleteByPrimaryKey(Integer detail_id);

    int insert(OrderDetail record);

    int insertSelective(OrderDetail record);

    OrderDetail selectByPrimaryKey(Integer detail_id);

    int updateByPrimaryKeySelective(OrderDetail record);

    int updateByPrimaryKey(OrderDetail record);
}