package qidian.it.springbootalipay.mapper;

import qidian.it.springbootalipay.entity.Product;

public interface ProductMapper {
    int deleteByPrimaryKey(Integer product_id);

    int insert(Product record);

    int insertSelective(Product record);

    Product selectByPrimaryKey(Integer product_id);

    int updateByPrimaryKeySelective(Product record);

    int updateByPrimaryKey(Product record);
}