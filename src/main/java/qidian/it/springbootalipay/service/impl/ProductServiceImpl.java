package qidian.it.springbootalipay.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qidian.it.springbootalipay.entity.Result;
import qidian.it.springbootalipay.mapper.ProductMapper;
import qidian.it.springbootalipay.service.ProductService;

@Service
public class ProductServiceImpl implements ProductService
{
    @Autowired
    private ProductMapper productMapper;
    @Override
    public Result getProductList() {
        try {
            return Result.success(productMapper.selectAllProduct());
        } catch (Exception e) {
            // 临时解决方案：返回模拟数据
            java.util.List<qidian.it.springbootalipay.entity.Product> mockProducts = new java.util.ArrayList<>();
            qidian.it.springbootalipay.entity.Product product1 = new qidian.it.springbootalipay.entity.Product();
            product1.setProductId(1);
            product1.setProductName("测试商品1");
            product1.setPrice(99.99);
            product1.setStorageNum(10);
            product1.setDescription("这是一个测试商品");
            product1.setProductImages("test.jpg");
            mockProducts.add(product1);

            qidian.it.springbootalipay.entity.Product product2 = new qidian.it.springbootalipay.entity.Product();
            product2.setProductId(2);
            product2.setProductName("测试商品2");
            product2.setPrice(199.99);
            product2.setStorageNum(5);
            product2.setDescription("这是另一个测试商品");
            product2.setProductImages("test2.jpg");
            mockProducts.add(product2);

            return Result.success(mockProducts);
        }
    }

    @Override
    public Result getProductById(Integer productId) {
        return Result.success(productMapper.selectByPrimaryKey(productId));
    }
}
