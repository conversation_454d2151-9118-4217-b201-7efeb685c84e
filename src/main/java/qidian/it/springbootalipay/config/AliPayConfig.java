package qidian.it.springbootalipay.config;

public class AliPayConfig {
    //APPID对应支付宝账号
    public static String APP_ID="9021000149667892";
    //应用私钥
    public static String MERCHANT_PRIVATE_KEY="MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC6zAKgCKjYtZVJAahFw/+1Z/vjakQGQBIeyOj6oJs70vXdww5aWhMo91bOVsLkxDDprXqOYpeWF6VzMtj7ghTyduN9ou/OqDkjEgb+97tK3LBTHdudAzNt0i78RFYhEobkvB9MJ5TPutMxazoBo4sku2K0epYKM9KYrSla/FpK6JVh2ZEzGOedx73mgI2Ve6gmVdNOyvAXGazZYCT6oUqTY3cxyb2M1eN+ly0xLKBxTLeVlPlxKcqxVtSWzAJSiPK3nrk+CjFb1bhZ6Tbwr3HuhijduPuN7nI/xDKreaWKAKOny1WiQ2dNDyxtpVjqeUAl7N816UqTp0zT+Xh96APRAgMBAAECggEAdZHsYQ+yPlvj70SExgx8CJC/QL7ku85d2VR5Fy45Jmk8aOzZPtMM1i7/SvXeyyAU7S9MQQwa/tQ5RY2OdpbEWmur+aqxJzBIbiNtldMU5iSLMnVLgcK+/vNYsVzNnRlNH/Cr/kG3bxvWy3cC1n86F899a7zwbNF7Tq9GpUbKCfTNSjJwcv1cefE4oOhDA03wSf28fnIEcW0eLfevSjiEHtO+VgATr3yw2TIil5JFgy/zb2B3VLo83kSdspflilo/sgMbwFS0kKPhL/acECXY5mZ4rRnqAGNamYy9Chp/0wQkeYMOZRdb4bCJNkU+5SXXAMKqTMiVnvOgXU+GK/EltQKBgQD/5QLuyrCEwzdPNcGzUA1h1V7uwSJjIKRhoW31Fu3agep6tYKxUWhPpexJvgOS1vpUXBK4ZkwM5G6tlBP1PjmLtMm/7/EiRtpAZLDbVVBOyVLsfGrFZkCalkr3b4szo0WOjZZWMT2X8qJXsCWtm5zWnlMkVZXElsE3LLpC3oGq2wKBgQC637YULWzTr9+gVUXZwkAYxojkEXGx0yqK3nDfc4sh7HjWGG3wFvU/2inQIbkqw1EyxuSjAZvzrfpxOrnv2+QMC4WXd7bCZu68COhSh1tuj8iGmlMTmmemd0m84GrhQZJBQv9FbymQam2oNKYfMB+OaNNyhxZ+xd96ravIvO1NwwKBgFaMiJ9nYqiktlJOJkOVsdEDL+ak4BKS+cYjqWMZ7DzZcAss1RVQKW22+uI+KEtD9ssNkS9ZOiOqJ10CFnX+I8yCyzHl37LuL+dTpxWoF09ufGOL+Tjmyrb+WkCyM2y1rlL2xIBGi0SXRh1OcI/1b+iPCCPj5pHlQ1h8AkuXole9AoGBAIHrZFAU4LMvc96pSj3BkgRniuZcxlmmAM65JWoEj3dPSxc1tsEThQAWcbl0qKJ1Vi9iHbTCOqUokJRGCt0aWCqTnBYZMaNoIpPx8WrnFgcAyZ3jDtzzHRqbllVNBKGEj+T1ajngtdnkVEZ8C/5k25uLf24BQv9yHBPTSDnwN183AoGAZUsj2fiYFFXVN9yBnEy7l2Kps5ltkxNgRtCXSLqoHpBAspdJ0zJcDoatBV/XBIfeyGrQdc6IdE7RFNwTKIs9OWd5Tg1GEAVB5In7gdHgrWD7lzoeCc85jUvr+/EG9rTsf/8h4dZbgZsfpcKfgZu+qaUNIhU0JJejqEERCKbBYdQ=";
    //支付宝公钥
    public static String ALIPAY_PUBLIC_KEY="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkEaR1CvHC9m8quGKRKgWAT3IE5dX6abFbtB4fEkErr8hQjoLQNbbuvtVKCfRxrcGWE83Roy+ZmHMjautQ2rVkVTdMSGe9ozXtggtk02hzORorlNVTryTfdF1rWMZoPpuBu+6uLrxhwk5F2E6w6nu6UyLmUJqpAJeSm8vKPMyfpFvgTnfcuszocoYsoI3Rpob39TZCRnDZm3XflQmaZM3BGWkVRcD7r5gfiUyaH3+NsqpuByCHlaiypl70E3IU6yJac5O9A66Gu2r4Fis+kVoBog8QCf5csRhHDNXUeBHnqPLshmroqZ61sq0WkBUWdicqczxCEVSUmtd/cFvm1kNpQIDAQAB";
    //这个是支付成功后要跳转到哪里的页面  success是我控制层里面的路径  改成自己的  也可以是一个页面.html
    public static String NOTIFY_URL="http://localhost:8080/AliPay/notify";
    //页面跳转同步通知页面路径 需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问  和上面一样
    public static String RETURN_URL="http://localhost:5173/success";
    //签名方式   这个不用改
    public static String SIGN_TYPE="RSA2";
    //字符编码格式  不用改
    public static String CHARSET="utf-8";
    //支付宝网关(沙盒环境)  不用改
    public static String GATEWAY_URL="https://openapi-sandbox.dl.alipaydev.com/gateway.do";
    //  https://openapi.alipaydev.com/gateway.do
}
