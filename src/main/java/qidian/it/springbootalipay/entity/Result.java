package qidian.it.springbootalipay.entity;

public class Result {
    private Integer code;
    private String message;
    private Object data;//存放数据

    public Result() {
    }


    public Result(Integer code) {
        this.code = code;
    }

    public Result(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    public Result(Object data) {
        this.data = data;
    }

    public Result(Integer code, Object data) {
        this.code = code;
        this.data = data;
    }

    public Result(Integer code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }


    public static Result success(Integer code){
        return  new Result(code);
    }


    public static Result success(String message){
        return  new Result(200,message);
    }


    public static Result success(Object data){
        return  new Result(200,data);
    }


    public static Result success(Integer code,Object data){
        return  new Result(code,data);
    }

    public static Result success(String message,Object data){
        return  new Result(200,message,data);
    }

    public static Result fail(String message){
        return  new Result(500,message);
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "Result{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
