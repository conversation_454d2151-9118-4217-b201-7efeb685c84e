package qidian.it.springbootalipay.entity;

public class Product {
    private Integer product_id;

    private String product_name;

    private Double price;

    private Integer storage_num;

    private String description;

    private String product_images;

    public Integer getProduct_id() {
        return product_id;
    }

    public void setProduct_id(Integer product_id) {
        this.product_id = product_id;
    }

    public String getProduct_name() {
        return product_name;
    }

    public void setProduct_name(String product_name) {
        this.product_name = product_name == null ? null : product_name.trim();
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Integer getStorage_num() {
        return storage_num;
    }

    public void setStorage_num(Integer storage_num) {
        this.storage_num = storage_num;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getProduct_images() {
        return product_images;
    }

    public void setProduct_images(String product_images) {
        this.product_images = product_images == null ? null : product_images.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", product_id=").append(product_id);
        sb.append(", product_name=").append(product_name);
        sb.append(", price=").append(price);
        sb.append(", storage_num=").append(storage_num);
        sb.append(", description=").append(description);
        sb.append(", product_images=").append(product_images);
        sb.append("]");
        return sb.toString();
    }
}