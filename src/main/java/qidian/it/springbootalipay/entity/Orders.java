package qidian.it.springbootalipay.entity;

import java.util.Date;

public class Orders {
    private Integer order_id;

    private Integer user_id;

    private Double total_price;

    private Integer status;

    private Date create_time;

    public Integer getOrder_id() {
        return order_id;
    }

    public void setOrder_id(Integer order_id) {
        this.order_id = order_id;
    }

    public Integer getUser_id() {
        return user_id;
    }

    public void setUser_id(Integer user_id) {
        this.user_id = user_id;
    }

    public Double getTotal_price() {
        return total_price;
    }

    public void setTotal_price(Double total_price) {
        this.total_price = total_price;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", order_id=").append(order_id);
        sb.append(", user_id=").append(user_id);
        sb.append(", total_price=").append(total_price);
        sb.append(", status=").append(status);
        sb.append(", create_time=").append(create_time);
        sb.append("]");
        return sb.toString();
    }
}